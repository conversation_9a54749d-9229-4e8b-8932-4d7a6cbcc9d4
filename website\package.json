{"name": "freelancer-dz-website", "version": "1.0.0", "description": "Stunning static website showcasing FreeLancer DZ mobile app", "main": "index.html", "scripts": {"start": "npx serve . -p 3000", "dev": "npx live-server --port=3000 --open=/index.html", "build": "npm run optimize", "optimize": "npm run minify-css && npm run minify-js", "minify-css": "npx cleancss -o css/style.min.css css/style.css", "minify-js": "npx terser js/script.js -o js/script.min.js", "deploy": "npm run build && npm run upload", "upload": "echo 'Upload to your hosting provider'", "lighthouse": "npx lighthouse http://localhost:3000 --output html --output-path ./lighthouse-report.html", "test": "npm run lighthouse", "validate": "npx html-validate index.html", "format": "npx prettier --write *.html css/*.css js/*.js", "lint": "npx eslint js/*.js"}, "keywords": ["freelancer", "algeria", "flutter", "business-management", "static-website", "responsive", "pwa"], "author": "FreeLancer DZ Team", "license": "MIT", "devDependencies": {"clean-css-cli": "^5.6.2", "eslint": "^8.50.0", "html-validate": "^8.5.0", "lighthouse": "^11.0.0", "live-server": "^1.2.2", "prettier": "^3.0.3", "serve": "^14.2.1", "terser": "^5.20.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "repository": {"type": "git", "url": "https://github.com/freelancer-dz/website.git"}, "bugs": {"url": "https://github.com/freelancer-dz/website/issues"}, "homepage": "https://freelancerdz.com"}