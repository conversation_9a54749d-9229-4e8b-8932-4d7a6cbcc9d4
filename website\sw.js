// Service Worker for FreeLancer DZ Website
const CACHE_NAME = 'freelancer-dz-v1.0.0';
const urlsToCache = [
    '/',
    '/index.html',
    '/css/style.css',
    '/js/script.js',
    '/manifest.json',
    '/assets/favicon.ico',
    // External resources
    'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.js',
    'https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching files');
                return cache.addAll(urlsToCache);
            })
            .then(() => {
                console.log('Service Worker: Cached all files successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Cache failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activated successfully');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }

    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // Return cached version or fetch from network
                if (response) {
                    console.log('Service Worker: Serving from cache', event.request.url);
                    return response;
                }

                console.log('Service Worker: Fetching from network', event.request.url);
                return fetch(event.request).then((response) => {
                    // Don't cache if not a valid response
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }

                    // Clone the response
                    const responseToCache = response.clone();

                    caches.open(CACHE_NAME)
                        .then((cache) => {
                            cache.put(event.request, responseToCache);
                        });

                    return response;
                }).catch((error) => {
                    console.error('Service Worker: Fetch failed', error);
                    
                    // Return offline page for navigation requests
                    if (event.request.destination === 'document') {
                        return caches.match('/index.html');
                    }
                    
                    // Return a generic offline response for other requests
                    return new Response('Offline - Please check your internet connection', {
                        status: 503,
                        statusText: 'Service Unavailable',
                        headers: new Headers({
                            'Content-Type': 'text/plain'
                        })
                    });
                });
            })
    );
});

// Background sync for form submissions
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'contact-form') {
        event.waitUntil(syncContactForm());
    }
});

// Push notification handler
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'New update available for FreeLancer DZ!',
        icon: '/assets/icon-192x192.png',
        badge: '/assets/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'Explore Features',
                icon: '/assets/action-explore.png'
            },
            {
                action: 'download',
                title: 'Download App',
                icon: '/assets/action-download.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('FreeLancer DZ', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked', event.action);
    
    event.notification.close();

    event.waitUntil(
        clients.openWindow(event.action === 'download' ? '/#download' : '/#features')
    );
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
});

// Helper function to sync contact form data
async function syncContactForm() {
    try {
        // Get pending form submissions from IndexedDB
        const pendingForms = await getPendingForms();
        
        for (const form of pendingForms) {
            try {
                const response = await fetch('/api/contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(form.data)
                });
                
                if (response.ok) {
                    await removePendingForm(form.id);
                    console.log('Service Worker: Form synced successfully');
                }
            } catch (error) {
                console.error('Service Worker: Form sync failed', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

// Helper functions for IndexedDB operations
async function getPendingForms() {
    // This would typically use IndexedDB to store offline form submissions
    // For demo purposes, returning empty array
    return [];
}

async function removePendingForm(id) {
    // This would remove the form from IndexedDB after successful sync
    console.log('Service Worker: Removing synced form', id);
}

// Performance monitoring
self.addEventListener('fetch', (event) => {
    const startTime = performance.now();
    
    event.respondWith(
        caches.match(event.request).then((response) => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            // Log performance metrics
            if (duration > 1000) {
                console.warn('Service Worker: Slow response detected', {
                    url: event.request.url,
                    duration: duration,
                    cached: !!response
                });
            }
            
            return response || fetch(event.request);
        })
    );
});

// Cache management - clean up old entries
self.addEventListener('activate', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME).then((cache) => {
            return cache.keys().then((requests) => {
                // Remove entries older than 30 days
                const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
                
                return Promise.all(
                    requests.map((request) => {
                        return cache.match(request).then((response) => {
                            if (response) {
                                const dateHeader = response.headers.get('date');
                                const responseDate = dateHeader ? new Date(dateHeader).getTime() : 0;
                                
                                if (responseDate < thirtyDaysAgo) {
                                    console.log('Service Worker: Removing old cache entry', request.url);
                                    return cache.delete(request);
                                }
                            }
                        });
                    })
                );
            });
        })
    );
});

console.log('Service Worker: Script loaded successfully');
console.log('Service Worker: Version', CACHE_NAME);
