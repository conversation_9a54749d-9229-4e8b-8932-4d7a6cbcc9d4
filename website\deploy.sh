#!/bin/bash

# FreeLancer DZ Website Deployment Script
# This script optimizes and deploys the website

echo "🚀 Starting FreeLancer DZ Website Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Install npm packages if needed
install_packages() {
    if [ ! -d "node_modules" ]; then
        print_status "Installing npm packages..."
        npm install
        print_success "Packages installed successfully"
    else
        print_status "Packages already installed"
    fi
}

# Validate HTML
validate_html() {
    print_status "Validating HTML..."
    if command -v html-validate &> /dev/null; then
        npx html-validate index.html
        if [ $? -eq 0 ]; then
            print_success "HTML validation passed"
        else
            print_warning "HTML validation found issues"
        fi
    else
        print_warning "html-validate not available, skipping HTML validation"
    fi
}

# Optimize CSS
optimize_css() {
    print_status "Optimizing CSS..."
    if [ -f "css/style.css" ]; then
        npx cleancss -o css/style.min.css css/style.css
        print_success "CSS optimized and minified"
    else
        print_error "CSS file not found"
        exit 1
    fi
}

# Optimize JavaScript
optimize_js() {
    print_status "Optimizing JavaScript..."
    if [ -f "js/script.js" ]; then
        npx terser js/script.js -o js/script.min.js --compress --mangle
        print_success "JavaScript optimized and minified"
    else
        print_error "JavaScript file not found"
        exit 1
    fi
}

# Generate sitemap
generate_sitemap() {
    print_status "Generating sitemap..."
    cat > sitemap.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://freelancerdz.com/</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://freelancerdz.com/#features</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://freelancerdz.com/#demo</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://freelancerdz.com/#download</loc>
        <lastmod>$(date +%Y-%m-%d)</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
</urlset>
EOF
    print_success "Sitemap generated"
}

# Generate robots.txt
generate_robots() {
    print_status "Generating robots.txt..."
    cat > robots.txt << EOF
User-agent: *
Allow: /

Sitemap: https://freelancerdz.com/sitemap.xml

# Disallow admin areas
Disallow: /api/
Disallow: /.git/
Disallow: /node_modules/

# Allow important files
Allow: /css/
Allow: /js/
Allow: /assets/
Allow: /manifest.json
Allow: /sw.js
EOF
    print_success "robots.txt generated"
}

# Run Lighthouse audit
run_lighthouse() {
    print_status "Running Lighthouse audit..."
    if command -v lighthouse &> /dev/null; then
        # Start local server
        npx serve . -p 3001 &
        SERVER_PID=$!
        sleep 3
        
        # Run Lighthouse
        npx lighthouse http://localhost:3001 --output html --output-path ./lighthouse-report.html --quiet
        
        # Stop server
        kill $SERVER_PID
        
        print_success "Lighthouse audit completed. Check lighthouse-report.html"
    else
        print_warning "Lighthouse not available, skipping audit"
    fi
}

# Create deployment package
create_package() {
    print_status "Creating deployment package..."
    
    # Create dist directory
    mkdir -p dist
    
    # Copy files
    cp index.html dist/
    cp -r css dist/
    cp -r js dist/
    cp -r assets dist/
    cp manifest.json dist/
    cp sw.js dist/
    cp sitemap.xml dist/
    cp robots.txt dist/
    
    # Copy API if exists
    if [ -d "api" ]; then
        cp -r api dist/
    fi
    
    print_success "Deployment package created in dist/ directory"
}

# Main deployment process
main() {
    echo "🎯 FreeLancer DZ Website Deployment"
    echo "=================================="
    
    check_dependencies
    install_packages
    validate_html
    optimize_css
    optimize_js
    generate_sitemap
    generate_robots
    create_package
    
    if [ "$1" = "--audit" ]; then
        run_lighthouse
    fi
    
    echo ""
    print_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📁 Files are ready in the 'dist' directory"
    echo "🌐 Upload the contents of 'dist' to your web server"
    echo "🔍 Run with --audit flag to include Lighthouse audit"
    echo ""
    echo "Next steps:"
    echo "1. Upload dist/ contents to your hosting provider"
    echo "2. Configure your web server for SPA routing"
    echo "3. Set up SSL certificate for HTTPS"
    echo "4. Configure CDN for better performance"
    echo ""
    print_success "Happy deploying! 🚀"
}

# Run main function with all arguments
main "$@"
